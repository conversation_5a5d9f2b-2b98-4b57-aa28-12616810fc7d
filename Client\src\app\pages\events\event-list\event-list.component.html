<!-- Event List Container -->
<div class="event-list-container">
  <!-- Header with Title and Add Button -->
  <div class="d-flex justify-content-between align-items-center mb-2">
    <h2 class="mb-0">Events</h2>
    <div class="d-flex">
      <button
        pButton
        class="p-button-outlined p-button-danger me-2 filter-btn"
        (click)="toggleFilters()"
      >
        <i class="pi pi-filter me-1"></i>
        {{ showFilters ? "Hide" : "Filters" }}
      </button>
      <button
        pButton
        class="p-button-danger create-event-btn filter-btn"
        (click)="addEvent()"
        *ngIf="isGlobalAdmin || authService.hasRole('Event Organizer')"
      >
        <i class="pi pi-plus me-1"></i> Create New Event
      </button>
    </div>
  </div>

  <!-- Filter Section -->
  <div class="compact-filter-section bg-light" *ngIf="showFilters">
    <div
      class="filter-header d-flex justify-content-between align-items-center mb-2"
    >
      <h5 class="mb-0">Filter Events</h5>
      <p-button
        label="Clear Filters"
        icon="pi pi-filter-slash"
        styleClass="p-button-sm p-button-outlined p-button-secondary"
        [style]="{ height: '28px', 'font-size': '0.8rem' }"
        (onClick)="clearFilters()"
      ></p-button>
    </div>

    <!-- Progressive Disclosure Filter Section -->
    <!-- Basic Filters (Always Visible) -->
    <div class="basic-filter-section">
      <form [formGroup]="filterForm">
        <div class="basic-filter-row">
          <!-- Search Input -->
          <div class="basic-filter-item search-item">
            <div class="p-input-icon-left">
              <i class="pi pi-search"></i>
              <input
                type="text"
                pInputText
                formControlName="searchTerm"
                placeholder="Search events by name, organizer, or description..."
                class="search-input"
              />
            </div>
          </div>

          <!-- Status Dropdown -->
          <div class="basic-filter-item status-item">
            <p-dropdown
              formControlName="eventStatus"
              [options]="eventStatusOptions"
              optionLabel="name"
              optionValue="value"
              placeholder="All Status"
              styleClass="status-dropdown"
              [style]="{ width: '100%' }"
              [showClear]="true"
            >
              <ng-template pTemplate="selectedItem" let-selectedOption>
                <div class="status-selected-item" *ngIf="selectedOption">
                  <span class="status-label">Status:</span>
                  <span class="status-value">{{ selectedOption.name }}</span>
                </div>
              </ng-template>
            </p-dropdown>
          </div>

          <!-- More Filters Button -->
          <div class="basic-filter-item more-filters-item">
            <p-button
              label="More Filters"
              icon="pi pi-cog"
              styleClass="p-button-outlined more-filters-btn"
              (onClick)="openAdvancedFilters()"
              [badge]="
                activeFiltersCount > 0
                  ? activeFiltersCount.toString()
                  : undefined
              "
              badgeClass="p-badge-danger"
            ></p-button>
          </div>
        </div>
      </form>

      <!-- Active Filter Chips -->
      <div class="active-filters-section" *ngIf="activeFilterChips.length > 0">
        <div class="active-filters-header">
          <span class="active-filters-label">Active Filters:</span>
          <p-button
            label="Clear All"
            icon="pi pi-times"
            styleClass="p-button-text p-button-sm clear-all-btn"
            (onClick)="clearAllFilters()"
          ></p-button>
        </div>
        <div class="filter-chips-container">
          <p-chip
            *ngFor="let chip of activeFilterChips"
            [label]="chip.label"
            [removable]="true"
            (onRemove)="removeFilter(chip.key)"
            styleClass="filter-chip"
          ></p-chip>
        </div>
      </div>
    </div>
  </div>

  <!-- Advanced Filters Modal -->
  <p-dialog
    header="Advanced Filters"
    [(visible)]="showAdvancedFilters"
    [modal]="true"
    [closable]="true"
    [draggable]="false"
    [resizable]="false"
    styleClass="advanced-filters-dialog"
    [style]="{ width: '750px', maxHeight: '85vh', minHeight: '600px' }"
    [baseZIndex]="10000"
  >
    <form [formGroup]="advancedFilterForm" class="advanced-filters-form">
      <!-- Date & Time Section -->
      <div class="filter-section">
        <div class="section-header">
          <i class="pi pi-calendar section-icon"></i>
          <h4>Date & Time</h4>
        </div>

        <div class="filter-grid-dates">
          <!-- Event Start Date Range Row -->
          <div class="date-row">
            <div class="filter-group date-range-group">
              <label>Event Start Date</label>
              <div class="date-range-container">
                <p-calendar
                  formControlName="eventStartDateFrom"
                  placeholder="From Date"
                  [showIcon]="true"
                  dateFormat="dd/mm/yy"
                  styleClass="date-range-input"
                  [showClear]="true"
                ></p-calendar>
                <span class="date-separator">to</span>
                <p-calendar
                  formControlName="eventStartDateTo"
                  placeholder="To Date"
                  [showIcon]="true"
                  dateFormat="dd/mm/yy"
                  styleClass="date-range-input"
                  [showClear]="true"
                ></p-calendar>
              </div>
            </div>
          </div>

          <!-- Submitted and Reviewed Date Row -->
          <div class="date-row">
            <div class="filter-group">
              <label>Submitted Date</label>
              <p-calendar
                formControlName="submittedOn"
                placeholder="Select Date"
                [showIcon]="true"
                dateFormat="dd/mm/yy"
                styleClass="single-date-input"
                [showClear]="true"
              ></p-calendar>
            </div>

            <div class="filter-group">
              <label>Reviewed Date</label>
              <p-calendar
                formControlName="eventReviewedOn"
                placeholder="Select Date"
                [showIcon]="true"
                dateFormat="dd/mm/yy"
                styleClass="single-date-input"
                [showClear]="true"
              ></p-calendar>
            </div>
          </div>
        </div>
      </div>

      <!-- Classification Section -->
      <div class="filter-section">
        <div class="section-header">
          <i class="pi pi-tag section-icon"></i>
          <h4>Classification</h4>
        </div>

        <div class="filter-grid-classification">
          <div class="filter-group">
            <label>Event Type</label>
            <p-dropdown
              formControlName="type"
              [options]="typeOptions"
              optionLabel="name"
              optionValue="value"
              placeholder="All Types"
              styleClass="filter-dropdown"
              [showClear]="true"
            ></p-dropdown>
          </div>

          <div class="filter-group">
            <label>Category</label>
            <p-dropdown
              formControlName="category"
              [options]="categoryOptions"
              optionLabel="name"
              optionValue="value"
              placeholder="All Categories"
              styleClass="filter-dropdown"
              [showClear]="true"
            ></p-dropdown>
          </div>

          <div class="filter-group">
            <label>Approval Status</label>
            <p-dropdown
              formControlName="approvalStatus"
              [options]="approvalStatusOptions"
              optionLabel="name"
              optionValue="value"
              placeholder="All Status"
              styleClass="filter-dropdown"
              [showClear]="true"
            ></p-dropdown>
          </div>
        </div>
      </div>

      <!-- People Section -->
      <div class="filter-section">
        <div class="section-header">
          <i class="pi pi-users section-icon"></i>
          <h4>People</h4>
        </div>

        <div class="filter-grid-people">
          <div class="filter-group organizer-group">
            <label>Organizer</label>
            <p-dropdown
              formControlName="organizer"
              [options]="organizerOptions"
              optionLabel="name"
              optionValue="value"
              placeholder="All Organizers"
              styleClass="filter-dropdown organizer-dropdown"
              [filter]="true"
              filterPlaceholder="Search organizers..."
              [showClear]="true"
              [appendTo]="'body'"
              [panelStyle]="{ 'max-height': '200px', 'z-index': '10001' }"
            ></p-dropdown>
          </div>
        </div>
      </div>
    </form>

    <ng-template pTemplate="footer">
      <div class="dialog-footer">
        <p-button
          label="Clear All"
          icon="pi pi-filter-slash"
          styleClass="p-button-outlined p-button-secondary"
          (onClick)="clearAdvancedFilters()"
        ></p-button>
        <div class="footer-actions">
          <p-button
            label="Cancel"
            icon="pi pi-times"
            styleClass="p-button-outlined"
            (onClick)="cancelAdvancedFilters()"
          ></p-button>
          <p-button
            label="Apply Filters"
            icon="pi pi-check"
            styleClass="p-button-primary"
            (onClick)="applyAdvancedFilters()"
          ></p-button>
        </div>
      </div>
    </ng-template>
  </p-dialog>

  <!-- Tabs for All Events and Pending Review -->
  <div class="event-tabs">
    <ul class="nav nav-tabs">
      <li class="nav-item">
        <a
          class="nav-link"
          [class.active]="activeTab === 'all'"
          href="javascript:void(0)"
          (click)="setActiveTab('all')"
          >All</a
        >
      </li>
      <li class="nav-item">
        <a
          class="nav-link"
          [class.active]="activeTab === 'pending'"
          href="javascript:void(0)"
          (click)="setActiveTab('pending')"
        >
          Pending Review
          <span class="badge" *ngIf="pendingReviewCount > 0">{{
            pendingReviewCount
          }}</span>
        </a>
      </li>
    </ul>
  </div>

  <!-- Loading Spinner -->
  <div
    *ngIf="isLoading"
    class="d-flex justify-content-center align-items-center my-5"
  >
    <p-progressSpinner
      strokeWidth="4"
      [style]="{ width: '50px', height: '50px' }"
    ></p-progressSpinner>
  </div>

  <!-- Error Message -->
  <div *ngIf="error && !isLoading" class="alert alert-danger">
    <div class="mb-2">{{ error }}</div>
    <button pButton class="p-button-sm p-button-danger" (click)="loadEvents()">
      <i class="pi pi-refresh me-1"></i> Retry
    </button>
  </div>

  <!-- No Events Message -->
  <div
    *ngIf="!isLoading && !error && filteredEvents.length === 0"
    class="alert alert-info"
  >
    <div class="mb-2">No events found.</div>
    <!-- <button pButton class="p-button-sm p-button-info" (click)="clearFilters()">
      <i class="pi pi-filter-slash me-1"></i> Clear Filters
    </button> -->
  </div>

  <!-- Event Cards without scrolling - using pagination -->
  <div
    *ngIf="!isLoading && !error && filteredEvents.length > 0"
    class="events-container"
  >
    <div
      class="event-card-horizontal"
      *ngFor="let event of filteredEvents; trackBy: trackByEventId"
      (click)="viewEventDetails(event.id)"
    >
      <div class="event-card-content">
        <div class="event-image">
          <img
            [src]="
              event.eventImageUrl
                ? getFullImagePath(event.eventImageUrl)
                : 'assets/images/placeholder.jpg'
            "
            alt="Event Image"
            class="full-image"
          />
        </div>

        <div class="event-details">
          <div class="event-header">
            <div class="title-category">
              <h3 class="event-title">{{ event.title }}</h3>
              <div class="event-category">
                <span class="category-text">{{
                  formatEventType(event.typeName || "")
                }}</span>
                <span class="category-separator">•</span>
                <span class="category-text">{{
                  formatEventCategory(event.category)
                }}</span>
              </div>
            </div>
            <span
              class="status-badge"
              [ngClass]="{
                draft: event.statusName === 'Draft',
                pending: event.statusName === 'Submitted',
                approved:
                  event.statusName === 'Approved' && !hasEventStarted(event),
                'event-started':
                  event.statusName === 'Approved' && hasEventStarted(event),
                rejected: event.statusName === 'Rejected',
                cancelled: event.statusName === 'Cancelled',
                'pending-review': event.statusName === 'Submitted',
              }"
            >
              {{
                event.statusName === "Draft"
                  ? "Draft"
                  : event.statusName === "Submitted"
                    ? "Pending Review"
                    : event.statusName === "Approved" && hasEventStarted(event)
                      ? "Event Has Started"
                      : event.statusName
              }}
            </span>
            <button
              pButton
              class="p-button-rounded p-button-text edit-button"
              [ngClass]="{ 'disabled-edit-button': hasEventStarted(event) }"
              (click)="
                !hasEventStarted(event) && editEvent(event.id);
                $event.stopPropagation()
              "
              *ngIf="
                isGlobalAdmin ||
                event.organizerId === authService.getUserInfo()?.id
              "
              [pTooltip]="
                hasEventStarted(event)
                  ? 'Cannot edit event that has already started'
                  : ''
              "
              tooltipPosition="top"
            >
              <i class="pi pi-pencil"></i>
            </button>
          </div>

          <div class="event-info-container">
            <!-- Left Column -->
            <div class="event-info-column">
              <!-- Schedule Section -->
              <div class="event-info-row">
                <div class="info-label">Schedule on</div>
                <div class="info-value">
                  <i class="pi pi-calendar me-2"></i>
                  <span>
                    {{ event.eventStarts | date: "dd MMM yyyy" : "IST" }}
                    {{
                      event.displayStartTime && event.startTime
                        ? " - " + (event.startTime | slice: 0 : 5)
                        : ""
                    }}
                    To
                    {{ event.eventEnds | date: "dd MMM yyyy" : "IST" }}
                    {{
                      event.displayEndTime && event.endTime
                        ? " - " + (event.endTime | slice: 0 : 5)
                        : ""
                    }}
                  </span>
                </div>
              </div>

              <!-- Submitted By Section -->
              <div class="event-info-row">
                <div class="info-label">Submitted By</div>
                <div class="info-value">
                  <i class="pi pi-user me-2"></i>
                  <span>{{ event.submitterName || event.organizerName }}</span>
                </div>
              </div>

              <!-- Reviewed By Section - Only for Approved or Rejected events -->
              <div
                class="event-info-row reviewed-info"
                *ngIf="
                  event.statusName === 'Approved' ||
                  event.statusName === 'Rejected'
                "
              >
                <div class="info-label">Reviewed By</div>
                <div class="info-value">
                  <i class="pi pi-user me-2"></i>
                  <span>{{ event.reviewedByName || "Admin" }}</span>
                </div>
              </div>
            </div>

            <!-- Right Column -->
            <div class="event-info-column">
              <!-- Location Section -->
              <div class="event-info-row">
                <div class="info-label">Location</div>
                <div class="info-value">
                  <!-- Online Event -->
                  <ng-container
                    *ngIf="event.locationType === EventLocationType.Online"
                  >
                    <i class="pi pi-globe me-2"></i>
                    <span>Online Event</span>
                  </ng-container>

                  <!-- Venue Event -->
                  <ng-container
                    *ngIf="
                      event.locationType === EventLocationType.Venue &&
                      event.location?.address1 &&
                      event.location.address1 !== 'N/A'
                    "
                  >
                    <i class="pi pi-map-marker me-2"></i>
                    <span>
                      {{ event.location.address1 }}, {{ event.location.city }},
                      {{ event.location.state }}
                      {{ event.location.zipCode }}
                    </span>
                  </ng-container>

                  <!-- No Location Info -->
                  <ng-container
                    *ngIf="
                      !event.locationType ||
                      (event.locationType === EventLocationType.Venue &&
                        (!event.location?.address1 ||
                          event.location.address1 === 'N/A'))
                    "
                  >
                    <i class="pi pi-exclamation-triangle me-2"></i>
                    <span>Location not specified</span>
                  </ng-container>
                </div>
              </div>

              <!-- Submitted On Section -->
              <div class="event-info-row">
                <div class="info-label">Submitted On</div>
                <div class="info-value">
                  <i class="pi pi-calendar me-2"></i>
                  <span>{{
                    event.submittedOn || event.createdAt
                      | date: "dd MMM yyyy" : "IST"
                  }}</span>
                </div>
              </div>

              <!-- Reviewed On Section - Only for Approved or Rejected events -->
              <div
                class="event-info-row"
                *ngIf="
                  event.statusName === 'Approved' ||
                  event.statusName === 'Rejected'
                "
              >
                <div class="info-label">Reviewed On</div>
                <div class="info-value">
                  <i class="pi pi-calendar me-2"></i>
                  <span>{{
                    event.reviewedOn | date: "dd MMM yyyy" : "IST"
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="d-flex justify-content-between align-items-center mt-3 mb-5">
    <!-- Entries info text -->
    <div class="text-muted">
      Showing {{ (currentPage - 1) * pageSize + 1 }} to
      {{ Math.min(currentPage * pageSize, totalItems) }} of
      {{ totalItems }} entries
    </div>

    <!-- Pagination -->
    <nav aria-label="Page navigation" *ngIf="totalPages > 1">
      <ul class="pagination mb-0">
        <li class="page-item" [class.disabled]="currentPage === 1">
          <a
            class="page-link"
            href="javascript:void(0)"
            (click)="onPageChange(currentPage - 1)"
            tabindex="-1"
          >
            Previous
          </a>
        </li>

        <li
          class="page-item"
          *ngFor="let page of pages"
          [class.active]="page === currentPage"
        >
          <a
            class="page-link"
            href="javascript:void(0)"
            (click)="onPageChange(page)"
          >
            {{ page }}
          </a>
        </li>

        <li class="page-item" [class.disabled]="currentPage === totalPages">
          <a
            class="page-link"
            href="javascript:void(0)"
            (click)="onPageChange(currentPage + 1)"
          >
            Next
          </a>
        </li>
      </ul>
    </nav>
  </div>
</div>
