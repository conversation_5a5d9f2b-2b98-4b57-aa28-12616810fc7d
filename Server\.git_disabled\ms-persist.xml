<?xml version="1.0" encoding="utf-8"?>
<PendingCommit>
  <CommitComment>Enhance Docker support and configuration settings

- Updated `FilesController.cs` to comment on S3 usage and removed a console log.
- Modified `.dockerignore` to exclude unnecessary files from the Docker image.
- Added `appsettings.Docker.json` for connection strings, logging, JWT, and AWS S3 settings.
- Created a multi-stage `Dockerfile` for building and publishing the .NET application.
- Introduced `init-db.sh` to automate database initialization and migrations.
</CommitComment>
  <PinnedBranches />
  <PublishPrompt Enabled="True" />
  <ActiveAccountPrompt Enabled="True" />
  <RepositorySorts />
  <CreatePullRequest_DefaultTargetBranch />
</PendingCommit>